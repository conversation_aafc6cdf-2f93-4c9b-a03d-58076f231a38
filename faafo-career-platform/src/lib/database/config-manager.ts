/**
 * Database Configuration Manager
 * Centralized database configuration with environment-specific optimizations
 */

import { z } from 'zod';

// Configuration validation schemas
const DatabaseConfigSchema = z.object({
  url: z.string().min(1, 'Database URL is required'),
  maxConnections: z.number().min(1).max(100),
  minConnections: z.number().min(1).max(50),
  connectionTimeout: z.number().min(1000).max(60000),
  queryTimeout: z.number().min(1000).max(30000),
  idleTimeout: z.number().min(5000).max(300000),
  maxLifetime: z.number().min(60000).max(7200000),
  ssl: z.boolean().optional(),
  pooling: z.boolean().optional(),
  retryAttempts: z.number().min(0).max(10),
  retryDelay: z.number().min(100).max(10000),
  healthCheckInterval: z.number().min(5000).max(300000),
});

const CacheConfigSchema = z.object({
  enabled: z.boolean(),
  defaultTTL: z.number().min(60).max(86400),
  maxCacheSize: z.number().min(100).max(10000),
  queryCache: z.boolean(),
  resultCache: z.boolean(),
});

const PerformanceConfigSchema = z.object({
  slowQueryThreshold: z.number().min(100).max(10000),
  enableQueryLogging: z.boolean(),
  enableMetrics: z.boolean(),
  metricsRetention: z.number().min(1000).max(100000),
  autoOptimization: z.boolean(),
});

export type DatabaseConfig = z.infer<typeof DatabaseConfigSchema>;
export type CacheConfig = z.infer<typeof CacheConfigSchema>;
export type PerformanceConfig = z.infer<typeof PerformanceConfigSchema>;

export interface EnvironmentConfig {
  database: DatabaseConfig;
  cache: CacheConfig;
  performance: PerformanceConfig;
  environment: 'development' | 'staging' | 'production';
}

class DatabaseConfigManager {
  private config: EnvironmentConfig;
  private readonly environment: string;

  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  /**
   * Load configuration based on environment
   */
  private loadConfiguration(): EnvironmentConfig {
    const baseConfig = this.getBaseConfiguration();
    
    switch (this.environment) {
      case 'production':
        return this.getProductionConfiguration(baseConfig);
      case 'staging':
        return this.getStagingConfiguration(baseConfig);
      default:
        return this.getDevelopmentConfiguration(baseConfig);
    }
  }

  /**
   * Get base configuration from environment variables
   */
  private getBaseConfiguration(): EnvironmentConfig {
    return {
      database: {
        url: process.env.DATABASE_URL || '',
        maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
        minConnections: parseInt(process.env.DB_MIN_CONNECTIONS || '2'),
        connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
        queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '5000'),
        idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
        maxLifetime: parseInt(process.env.DB_MAX_LIFETIME || '3600000'),
        ssl: process.env.DB_SSL === 'true',
        pooling: process.env.DB_POOLING !== 'false',
        retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
        retryDelay: parseInt(process.env.DB_RETRY_DELAY || '1000'),
        healthCheckInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000'),
      },
      cache: {
        enabled: process.env.QUERY_CACHE_ENABLED !== 'false',
        defaultTTL: parseInt(process.env.QUERY_CACHE_TTL || '300000'),
        maxCacheSize: parseInt(process.env.QUERY_CACHE_SIZE || '1000'),
        queryCache: process.env.QUERY_CACHE_ENABLED !== 'false',
        resultCache: process.env.RESULT_CACHE_ENABLED !== 'false',
      },
      performance: {
        slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD || '1000'),
        enableQueryLogging: process.env.ENABLE_QUERY_LOGGING !== 'false',
        enableMetrics: process.env.ENABLE_METRICS !== 'false',
        metricsRetention: parseInt(process.env.METRICS_RETENTION || '10000'),
        autoOptimization: process.env.AUTO_OPTIMIZATION === 'true',
      },
      environment: this.environment as any,
    };
  }

  /**
   * Production-optimized configuration
   */
  private getProductionConfiguration(base: EnvironmentConfig): EnvironmentConfig {
    return {
      ...base,
      database: {
        ...base.database,
        maxConnections: Math.max(base.database.maxConnections, 30),
        minConnections: Math.max(base.database.minConnections, 5),
        connectionTimeout: Math.min(base.database.connectionTimeout, 15000),
        queryTimeout: Math.min(base.database.queryTimeout, 10000),
        ssl: true, // Always use SSL in production
        healthCheckInterval: 60000, // 1 minute
      },
      cache: {
        ...base.cache,
        enabled: true,
        defaultTTL: Math.max(base.cache.defaultTTL, 600000), // 10 minutes
        maxCacheSize: Math.max(base.cache.maxCacheSize, 2000),
      },
      performance: {
        ...base.performance,
        slowQueryThreshold: 500, // Stricter in production
        enableQueryLogging: false, // Reduce overhead
        enableMetrics: true,
        autoOptimization: true,
      },
    };
  }

  /**
   * Staging configuration
   */
  private getStagingConfiguration(base: EnvironmentConfig): EnvironmentConfig {
    return {
      ...base,
      database: {
        ...base.database,
        maxConnections: Math.min(base.database.maxConnections, 15),
        ssl: true,
      },
      cache: {
        ...base.cache,
        enabled: true,
        defaultTTL: Math.min(base.cache.defaultTTL, 300000), // 5 minutes
      },
      performance: {
        ...base.performance,
        enableQueryLogging: true,
        enableMetrics: true,
      },
    };
  }

  /**
   * Development configuration
   */
  private getDevelopmentConfiguration(base: EnvironmentConfig): EnvironmentConfig {
    return {
      ...base,
      database: {
        ...base.database,
        maxConnections: Math.min(base.database.maxConnections, 10),
        minConnections: 1,
        ssl: false, // Optional in development
      },
      cache: {
        ...base.cache,
        enabled: true,
        defaultTTL: 60000, // 1 minute for faster development
        maxCacheSize: 500,
      },
      performance: {
        ...base.performance,
        enableQueryLogging: true,
        enableMetrics: true,
        autoOptimization: false, // Manual control in development
      },
    };
  }

  /**
   * Validate configuration
   */
  private validateConfiguration(): void {
    try {
      DatabaseConfigSchema.parse(this.config.database);
      CacheConfigSchema.parse(this.config.cache);
      PerformanceConfigSchema.parse(this.config.performance);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(issue => 
          `${issue.path.join('.')}: ${issue.message}`
        ).join(', ');
        throw new Error(`Database configuration validation failed: ${issues}`);
      }
      throw error;
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): EnvironmentConfig {
    return { ...this.config };
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig(): DatabaseConfig {
    return { ...this.config.database };
  }

  /**
   * Get cache configuration
   */
  getCacheConfig(): CacheConfig {
    return { ...this.config.cache };
  }

  /**
   * Get performance configuration
   */
  getPerformanceConfig(): PerformanceConfig {
    return { ...this.config.performance };
  }

  /**
   * Update configuration (runtime updates)
   */
  updateConfig(updates: Partial<EnvironmentConfig>): void {
    this.config = {
      ...this.config,
      ...updates,
      database: { ...this.config.database, ...updates.database },
      cache: { ...this.config.cache, ...updates.cache },
      performance: { ...this.config.performance, ...updates.performance },
    };
    
    this.validateConfiguration();
  }

  /**
   * Get optimized connection string
   */
  getOptimizedConnectionString(): string {
    const dbConfig = this.config.database;
    const url = new URL(dbConfig.url);

    // Add connection parameters
    url.searchParams.set('connection_limit', dbConfig.maxConnections.toString());
    url.searchParams.set('pool_timeout', '10');
    url.searchParams.set('connect_timeout', (dbConfig.connectionTimeout / 1000).toString());
    url.searchParams.set('statement_timeout', dbConfig.queryTimeout.toString());
    url.searchParams.set('idle_in_transaction_session_timeout', dbConfig.idleTimeout.toString());

    // SSL configuration
    if (dbConfig.ssl && !url.searchParams.has('sslmode')) {
      url.searchParams.set('sslmode', 'require');
    }

    // Application identification
    url.searchParams.set('application_name', `faafo-career-platform-${this.environment}`);

    return url.toString();
  }

  /**
   * Get configuration summary for monitoring
   */
  getConfigSummary(): {
    environment: string;
    database: {
      maxConnections: number;
      connectionTimeout: number;
      ssl: boolean;
    };
    cache: {
      enabled: boolean;
      ttl: number;
    };
    performance: {
      slowQueryThreshold: number;
      metricsEnabled: boolean;
    };
  } {
    return {
      environment: this.environment,
      database: {
        maxConnections: this.config.database.maxConnections,
        connectionTimeout: this.config.database.connectionTimeout,
        ssl: this.config.database.ssl || false,
      },
      cache: {
        enabled: this.config.cache.enabled,
        ttl: this.config.cache.defaultTTL,
      },
      performance: {
        slowQueryThreshold: this.config.performance.slowQueryThreshold,
        metricsEnabled: this.config.performance.enableMetrics,
      },
    };
  }
}

// Singleton instance
export const dbConfigManager = new DatabaseConfigManager();

export default dbConfigManager;
